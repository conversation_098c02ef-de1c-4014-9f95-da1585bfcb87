package main

import (
	"encoding/json"
	"fmt"
	"jiaredou/com_util"
	"sort"
	"strings"
)

func main() {
	keyId := "1101"
	secret := "PoePZKyk2RstfroFC9P9suKwsFthb1mS"

	content := map[string]interface{}{
		"aweme_id": "1012643180", // 抖音号
		//"partner_code": "48240695911", // 合作码
		"advertiser_id": 1835231957090313, // 账户id
	}
	time := com_util.DateFormat(com_util.GetNowTime().Unix(), "20060102150405")
	comReq := map[string]interface{}{
		"key_id":  keyId,
		"time":    time,
		"content": content,
	}

	comReq["sign"] = GetSign(secret, map[string]string{
		"key_id": keyId,
		"time":   time,
	})

	req, _ := json.Marshal(&comReq)
	fmt.Println("req", string(req))
}

func GetSign(secret string, data map[string]string) string {
	keys := make([]string, 0, len(data))
	for k, _ := range data {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	str := ""
	for _, key := range keys {
		str += key + "=" + data[key] + "&"
	}
	str = strings.TrimRight(str, "&") + secret

	return com_util.Md5(str)
}
