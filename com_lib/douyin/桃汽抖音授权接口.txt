1.查询账号详情
http://122.51.83.146:8021/taoqi/util/douyin_aweme_info
请求示例：
{"content":{"aweme_id":"1012643180"},"key_id":"1101","sign":"43b371b612ba2dc29580bf8450d6bcc3","time":"20250618112309"}
响应成功示例：
{
  "code": 0,
  "data": {
    "avatar_url": "",  头像
    "aweme_id": "",  抖音号
    "fans_count": , 粉丝数
    "nickname": "" 抖音昵称
  },
  "msg": ""
}

2.发送授权
http://122.51.83.146:8021/taoqi/util/douyin_send_auth
请求示例：partner_code合作码
{"content":{"aweme_id":"1012643180","partner_code":"48240695911"},"key_id":"1101","sign":"4e748d07760d58826b4168e979318052","time":"20250618113038"}
响应成功示例：
{
  "code": 0,
  "data": {
    "advertiser_id": 1835231957090313, 账户id
    "aweme_id": "1012643180" 抖音号
  },
  "msg": ""
}

3.查询授权状态
http://122.51.83.146:8021/taoqi/util/douyin_check_auth_status"
请求示例：advertiser_id 账户id
{"content":{"advertiser_id":1835231957090313,"aweme_id":"1012643180"},"key_id":"1101","sign":"d9856e56b62dbc9e35bf93bd63186c0c","time":"20250618113417"}
响应成功示例：
{
  "code": 0,
  "data": {
    "auth_status": false
  },
  "msg": ""
}